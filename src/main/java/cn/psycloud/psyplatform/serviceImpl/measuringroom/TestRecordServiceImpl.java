package cn.psycloud.psyplatform.serviceImpl.measuringroom;

import cn.hutool.core.date.DateUtil;
import cn.psycloud.psyplatform.dao.anteroom.UserDao;
import cn.psycloud.psyplatform.dao.measuringroom.ScaleDao;
import cn.psycloud.psyplatform.dao.measuringroom.ScaleFactorDao;
import cn.psycloud.psyplatform.dao.measuringroom.ScaleQuestionDao;
import cn.psycloud.psyplatform.dao.measuringroom.TestRecordDao;
import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.dto.measuringroom.*;
import cn.psycloud.psyplatform.entity.measuringroom.ScaleQuestionEntity;
import cn.psycloud.psyplatform.entity.measuringroom.TestRecordChartsEntity;
import cn.psycloud.psyplatform.entity.measuringroom.TestRecordEntity;
import cn.psycloud.psyplatform.entity.measuringroom.TestRecordExplainEntity;
import cn.psycloud.psyplatform.service.measuringroom.TestRecordService;
import cn.psycloud.psyplatform.service.measuringroom.TestScoreService;
import cn.psycloud.psyplatform.util.Base64Util;
import cn.psycloud.psyplatform.util.CommonHelper;
import cn.psycloud.psyplatform.util.POIWordHelper;
import cn.psycloud.psyplatform.util.PermissonHelper;
import com.deepoove.poi.data.PictureRenderData;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DateUtil.ageOfNow;

@Service
public class TestRecordServiceImpl implements TestRecordService {
    @Autowired
    private TestRecordDao testRecordDao;
    @Autowired
    private ScaleDao scaleDao;
    @Autowired
    private ScaleFactorDao scaleFactorDao;
    @Autowired
    private UserDao userDao;
    @Autowired
    private ScaleQuestionDao scaleQuestionDao;
    @Autowired
    private TestScoreService testScoreService;
    @Autowired
    private POIWordHelper poiWordHelper;
    @Value("${file.location}")
    String uploadPath;

    /**
     *  根据记录ID查询测评记录
     * @param recordId 记录id
     * @return 测评记录实体对象
     */
    @Override
    public TestRecordDto getById(Integer recordId) {
        var testRecord = testRecordDao.getById(recordId);
        var scale = scaleDao.getById(testRecord.getScaleId());
        List<ScaleQuestionEntity> listQuestions = scaleQuestionDao.getListByScaleIdForTestIng(scale.getId());
        scale.setListQuestions(listQuestions);
        var userDto = new UserDto();
        userDto.setUserId(testRecord.getUser().getUserId());
        var user = userDao.get(userDto);
        testRecord.setScale(scale);
        testRecord.setUser(user);
        return  testRecord;
    }

    /**
     *  查询测评记录集合：分页
     * @param dto 测评记录实体对象
     * @return 测评记录集合
     */
    @Override
    public BSDatatableRes<TestRecordDto> getListByPaged(TestRecordDto dto) {
        var dtRes = new BSDatatableRes<TestRecordDto>();
        var childStructsIds = PermissonHelper.getChildStructIds(dto.getStructId());
        dto.setChildStructs(childStructsIds);
        PageHelper.startPage(dto.getPageIndex()/dto.getPageSize()+1,dto.getPageSize());
        List<TestRecordDto> listRecords = testRecordDao.getList(dto);
        PageInfo<TestRecordDto> testRecordDto = new PageInfo<>(listRecords);
        dtRes.setData(testRecordDto.getList());
        dtRes.setRecordsTotal((int)testRecordDto.getTotal());
        dtRes.setRecordsFiltered((int)testRecordDto.getTotal());
        return dtRes;
    }

    /**
     * 根据条件查询测评记录集合
     * @param dto 测评记录实体对象
     * @return 测评记录集合
     */
    @Override
    public List<TestRecordDto> getList(TestRecordDto dto) {
        var childStructsIds = PermissonHelper.getChildStructIds(dto.getStructId());
        dto.setChildStructs(childStructsIds);
        return testRecordDao.getList(dto);
    }

    /**
     *  获取我的测评记录
     * @param dto 查询条件
     * @return 测评记录集合
     */
    public BSDatatableRes<TestRecordDto> getMyRecords(TestRecordDto dto){
        var dtRes = new BSDatatableRes<TestRecordDto>();
        // 修复分页计算：pageIndex是偏移量，需要转换为页码
        int pageNum = dto.getPageIndex() / dto.getPageSize() + 1;
        PageHelper.startPage(pageNum, dto.getPageSize());
        List<TestRecordDto> listRecords = testRecordDao.getMyRecords(dto);
        PageInfo<TestRecordDto> testRecordDto = new PageInfo<>(listRecords);
        dtRes.setData(testRecordDto.getList());
        dtRes.setRecordsTotal((int)testRecordDto.getTotal());
        dtRes.setRecordsFiltered((int)testRecordDto.getTotal());
        return dtRes;
    }

    /**
     *  保存测评记录
     * @param entity 测评记录实体对象
     * @return 测评记录id
     */
    @Override
    public int addRecord(TestRecordEntity entity) {
        entity.setId(0);
        testRecordDao.addTestRecord(entity);
        return  entity.getId();
    }

    /**
     *  删除
     * @param id 记录id
     * @return 影响行数
     */
    @Override
    public int delete(Integer id) {
        return testRecordDao.deleteByRecordId(id);
    }

    /**
     *  批量删除
     * @param ids id集合
     * @return 执行是否成功
     */
    @Override
    public boolean batchDel(String ids) {
        boolean isSuccess = false;
        var arrayIds = ids.split(",");
        for (String arrayId : arrayIds) {
            var id = Integer.parseInt(arrayId);
            isSuccess = delete(id) > 0 ;
        }
        return isSuccess;
    }

    /**
     *  更新测评状态
     * @param recordId 记录id
     * @param state 测评状态：0-未完成 1-已完成 2-测谎未通过
     * @return 影响行数
     */
    @Override
    public int updateTestState(Integer recordId, Integer state) {
        var map = new HashMap<String, Integer>();
        map.put("recordId",recordId);
        map.put("state",state);
        return testRecordDao.updateTestState(map);
    }

    /**
     *  更新测评开始时间
     * @param recordId 记录id
     * @return 影响行数
     */
    @Override
    public int updateTestStartTime(Integer recordId) {
        var map = new HashMap<String, Object>();
        map.put("recordId",recordId);
        map.put("startTime",new Date());
        return testRecordDao.updateStartTime(map);
    }

    /**
     *  验证个人信息是否符合量表要求
     * @param dto 测评记录实体对象
     * @return 是否符合
     */
    @Override
    public boolean isUserInfoComplete(TestRecordDto dto) {
        boolean check = false;
        var limit = dto.getScale().getTestLimit();
        if (limit == null || "".equals(limit) ) {
            check = true;
        }
        else {
            var userDto = new UserDto();
            userDto.setUserId(dto.getUser().getUserId());
            var user = userDao.get(userDto);
            if (limit.contains("出生年月")) {
                if (user.getBirth() != null && user.getBirth() != "") {
                    String[] arrayAgeLimit = dto.getScale().getAgeLimit().split(",");
                    if ("0".equals(arrayAgeLimit[0]) && "0".equals(arrayAgeLimit[1]))
                        check = true;
                    else {
                        var age = ageOfNow(user.getBirth());
                        if (age >= Integer.parseInt(arrayAgeLimit[0]) && age <= Integer.parseInt(arrayAgeLimit[1])) {
                            check = true;
                        }
                    }
                }
            }
            if (limit.contains("性别")) check = user.getSex() != null && !"".equals(user.getSex());
            if (limit.contains("姓名")) check = user.getRealName() != null && !"".equals(user.getRealName());
        }
        return check;
    }

    /**
     *  判断是否异常
     * @param recordId 记录id
     * @return 异常标识
     */
    @Override
    public int isAbnormal(Integer recordId) {
        return testRecordDao.isAbnormal(recordId);
    }

    /**
     *  查询九型人格测评记录集合：分页
     * @param dto 九型人格测评实体对象
     * @return 记录集合
     */
    @Override
    public BSDatatableRes<NineHouseStatDto> getNineHouseList(NineHouseStatDto dto) {
        var dtRes = new BSDatatableRes<NineHouseStatDto>();
        var childStructsIds = PermissonHelper.getChildStructIds(dto.getStructId());
        dto.setChildStructs(childStructsIds);
        PageHelper.startPage(dto.getPageIndex()/dto.getPageSize()+1,dto.getPageSize());
        List<NineHouseStatDto> listRecords = testRecordDao.getNineHouseList(dto);
        PageInfo<NineHouseStatDto> testRecordDto = new PageInfo<>(listRecords);
        dtRes.setData(testRecordDto.getList());
        dtRes.setRecordsTotal((int)testRecordDto.getTotal());
        dtRes.setRecordsFiltered((int)testRecordDto.getTotal());
        return  dtRes;
    }

    /**
     *  保存测评结果解释
     * @param entity 测评结果解释实体对象
     * @return 操作是否成功
     */
    @Override
    public boolean saveTestRecordExplain(TestRecordExplainEntity entity){
        return testRecordDao.saveTestRecordExplain(entity) > 0 ;
    }

    /**
     * 获取因子结果解释
     * @param recordId 测评记录id
     * @return 因子结果解释列表
     */
    @Override
    public List<TestRecordExplainEntity> getFactorExplains(Integer recordId) {
        return testRecordDao.getFactorExplains(recordId);
    }

    /**
     *  获取报告图表
     * @param recordId 测评记录id
     * @return 测评报告图表集合
     */
    public List<TestRecordChartsEntity> getReportCharts(Integer recordId) {
        return testRecordDao.getReportCharts(recordId);
    }

    /**
     *  保存测评报告里的图表
     * @param dto 测评报告图表实体对象
     * @return 操作是否成功
     */
    @Override
    public boolean saveTestRecordCharts(TestRecordChartsDto dto) {
        var isSuccess = false;
        testRecordDao.delTestRecordCharts(dto.getRecordId());
        if(dto.getChartsImg() != null) {
            int chartOrder = 0;
            for(String chartImg: dto.getChartsImg()){
                var fileName = CommonHelper.getGUID()+".png";
                Base64Util.generateImage(chartImg.replace("data:image/png;base64,",""),uploadPath+"/charts/"+fileName);
                var chartsEntity = new TestRecordChartsEntity();
                chartsEntity.setRecordId(dto.getRecordId());
                chartsEntity.setChartsImg(fileName);
                chartsEntity.setChartOrder(chartOrder++);
                chartsEntity.setFactorType(dto.getFactorType());
                chartsEntity.setChartType(dto.getChartType());
                chartsEntity.setChartIndex(dto.getChartIndex());
                isSuccess = testRecordDao.saveTestRecordCharts(chartsEntity) > 0;
            }
        }
        return isSuccess;
    }

    /**
     *  保存测评报告里的图表（新版本，支持详细图表信息）
     * @param requestDto 测评报告图表请求实体对象
     * @return 操作是否成功
     */
    @Override
    public boolean saveTestRecordChartsV2(TestRecordChartsRequestDto requestDto) {
        var isSuccess = false;
        testRecordDao.delTestRecordCharts(requestDto.getRecordId());
        if(requestDto.getChartsImg() != null && !requestDto.getChartsImg().isEmpty()) {
            int chartOrder = 0;
            for(ChartInfoDto chartInfo: requestDto.getChartsImg()){
                var fileName = CommonHelper.getGUID()+".png";
                String imageData = chartInfo.getImageData();
                if(imageData != null) {
                    imageData = imageData.replace("data:image/png;base64,","");
                    Base64Util.generateImage(imageData, uploadPath+"/charts/"+fileName);

                    var chartsEntity = new TestRecordChartsEntity();
                    chartsEntity.setRecordId(requestDto.getRecordId());
                    chartsEntity.setChartsImg(fileName);
                    chartsEntity.setChartOrder(chartOrder++);
                    chartsEntity.setFactorType(chartInfo.getFactorType());
                    chartsEntity.setChartType(chartInfo.getChartType());
                    chartsEntity.setChartIndex(chartInfo.getChartIndex());
                    isSuccess = testRecordDao.saveTestRecordCharts(chartsEntity) > 0;
                }
            }
        }
        return isSuccess;
    }

    /**
     *  导出测评记录
     * @param dto 查询条件
     * @return  测评记录集合
     */
    @Override
    public List<ExportTestRecordDto> getExportTestRecordList(TestRecordDto dto) {
        var childStructsIds = PermissonHelper.getChildStructIds(dto.getStructId());
        dto.setChildStructs(childStructsIds);
        return testRecordDao.getExportTestRecordList(dto);
    }

    /**
     *  测评报告导出word
     * @param recordId 测评记录Id
     * @return 文件名
     */
    @Override
    public String exportTestReportToWord(Integer recordId, String folderName){
        String filePath = "";
        try {
            var reportDto = getReport(recordId);
            if(reportDto.getListExplains() != null && reportDto.getListExplains().size() > 0) {
                Map<String, Object> reportMap = new HashMap<>();

                // 构建基本信息
                buildBasicInfo(reportDto, reportMap);

                // 构建因子章节数据
                List<WordReportSection> factorSections = buildFactorSections(reportDto.getListExplains());
                reportMap.put("factorSections", factorSections);

                // 生成所有因子的表格数据（扁平化）
                List<Map<String, String>> allTableRows = new ArrayList<>();
                StringBuilder sectionTitles = new StringBuilder();

                for (WordReportSection section : factorSections) {
                    if (section.getFactors() != null && !section.getFactors().isEmpty()) {
                        // 添加章节标题行
                        Map<String, String> titleRow = new HashMap<>();
                        titleRow.put("factorName", "【" + section.getSectionTitle() + "】");
                        titleRow.put("interpretation", "");
                        allTableRows.add(titleRow);

                        // 添加因子数据行
                        for (WordReportFactor factor : section.getFactors()) {
                            Map<String, String> row = new HashMap<>();
                            row.put("factorName", factor.getFactorName());
                            row.put("interpretation", factor.getInterpretation());
                            allTableRows.add(row);
                        }

                        // 添加空行分隔
                        Map<String, String> emptyRow = new HashMap<>();
                        emptyRow.put("factorName", "");
                        emptyRow.put("interpretation", "");
                        allTableRows.add(emptyRow);
                    }
                }
                reportMap.put("tableRows", allTableRows);

                // 生成美观的表格格式文本
                StringBuilder factorContent = new StringBuilder();
                for (WordReportSection section : factorSections) {
                    if (section.getFactors() != null && !section.getFactors().isEmpty()) {
                        // 章节标题
                        factorContent.append("【").append(section.getSectionTitle()).append("】\n\n");

                        // 表格头部
                        factorContent.append("┌────────────────────┬────────────────────────────────────────────────────────────┐\n");
                        factorContent.append("│      因子名称      │                        结果解释                            │\n");
                        factorContent.append("├────────────────────┼────────────────────────────────────────────────────────────┤\n");

                        // 表格内容
                        for (WordReportFactor factor : section.getFactors()) {
                            String factorName = factor.getFactorName();
                            String interpretation = factor.getInterpretation();

                            // 处理长文本换行
                            factorContent.append("│ ").append(String.format("%-18s", factorName)).append(" │ ");
                            factorContent.append(String.format("%-58s", interpretation.length() > 58 ?
                                interpretation.substring(0, 55) + "..." : interpretation));
                            factorContent.append(" │\n");
                        }

                        // 表格底部
                        factorContent.append("└────────────────────┴────────────────────────────────────────────────────────────┘\n\n");
                    }
                }
                reportMap.put("factorContent", factorContent.toString());

                String templatePath = CommonHelper.getResourcesFilePath("static/template/report_new.docx");
                String fileName = reportDto.getTestRecord().getId() + "_" + reportDto.getTestRecord().getUser().getRealName() + "_" + reportDto.getTestRecord().getScale().getScaleName() + ".docx";
                filePath = String.format("report/%s/%s",folderName, fileName);
                String fileAbPath=String.format(uploadPath +"report/%s/%s", folderName, fileName);

                poiWordHelper.downloadWord(templatePath, fileAbPath, reportMap);
            }
        } catch (Exception e) {
            throw new RuntimeException("导出测评报告失败", e);
        }
        return  filePath;
    }

    /**
     *  批量导出测评报告
     * @param dto 查询条件
     * @return 文件路径
     */
    @Override
    public String batchExportReport(TestRecordDto dto) {
        String result = "";
        var childStructsIds = PermissonHelper.getChildStructIds(dto.getStructId());
        dto.setChildStructs(childStructsIds);
        var recordIds = testRecordDao.getRecordIdsForBatchExport(dto);
        if(!recordIds.isEmpty()) {
            result = DateUtil.format(new Date(), "yyyyMMddHHmmssSSS");
            for(Map<String,Integer> map: recordIds){
                exportTestReportToWord(map.get("id"), result);
            }
        }
        return  result;
    }

    /**
     * 获取因子结果解释（按父子关系组织）
     * @param recordId 测评记录id
     * @return 按父子关系组织的因子解释
     */
    @Override
    public List<FactorExplainHierarchyDto> getFactorExplainsWithHierarchy(Integer recordId) {
        // 获取因子解释
        List<TestRecordExplainEntity> explains = testRecordDao.getFactorExplains(recordId);

        // 获取测评记录以获取量表ID
        TestRecordDto testRecord = getById(recordId);
        Integer scaleId = testRecord.getScale().getId();

        // 获取该量表的所有因子信息（包含factorIds）
        List<ScaleFactorDto> factors = scaleFactorDao.getFactorsByScaleId(scaleId);

        // 创建因子ID到解释的映射
        Map<Integer, TestRecordExplainEntity> explainMap = explains.stream()
                .collect(Collectors.toMap(TestRecordExplainEntity::getFactorId, e -> e));

        // 创建因子ID到因子信息的映射
        Map<Integer, ScaleFactorDto> factorMap = factors.stream()
                .collect(Collectors.toMap(ScaleFactorDto::getId, f -> f));

        // 找出所有父因子（有factorIds的因子）
        List<ScaleFactorDto> parentFactors = factors.stream()
                .filter(f -> f.getFactorIds() != null && !f.getFactorIds().trim().isEmpty())
                .sorted(Comparator.comparing(ScaleFactorDto::getFactorType).reversed()) // 按类型倒序，高级因子在前
                .collect(Collectors.toList());

        // 构建层级结构
        List<FactorExplainHierarchyDto> result = new ArrayList<>();
        Set<Integer> usedFactorIds = new HashSet<>();

        for (ScaleFactorDto parentFactor : parentFactors) {
            if (explainMap.containsKey(parentFactor.getId()) && !usedFactorIds.contains(parentFactor.getId())) {
                FactorExplainHierarchyDto parentNode = buildFactorHierarchy(
                        parentFactor, explainMap, factorMap, usedFactorIds);
                if (parentNode != null) {
                    result.add(parentNode);
                }
            }
        }

        // 添加没有父因子的独立因子
        for (TestRecordExplainEntity explain : explains) {
            if (!usedFactorIds.contains(explain.getFactorId())) {
                FactorExplainHierarchyDto node = new FactorExplainHierarchyDto();
                node.setFactorId(explain.getFactorId());
                node.setFactorName(explain.getFactorName());
                node.setInterpretation(explain.getInterpretation());
                node.setOriginalScore(explain.getOriginalScore());
                node.setScore(explain.getScore());
                node.setChildren(new ArrayList<>());
                result.add(node);
            }
        }

        return result;
    }

    private FactorExplainHierarchyDto buildFactorHierarchy(
            ScaleFactorDto factor,
            Map<Integer, TestRecordExplainEntity> explainMap,
            Map<Integer, ScaleFactorDto> factorMap,
            Set<Integer> usedFactorIds) {

        if (!explainMap.containsKey(factor.getId())) {
            return null;
        }
        
        TestRecordExplainEntity explain = explainMap.get(factor.getId());
        FactorExplainHierarchyDto node = new FactorExplainHierarchyDto();
        
        // 设置因子基本信息
        node.setFactorId(explain.getFactorId());
        node.setFactorName(explain.getFactorName());
        node.setInterpretation(explain.getInterpretation());
        node.setFactorType(factor.getFactorType());
        node.setChildren(new ArrayList<>());
        node.setOriginalScore(explain.getOriginalScore());
        node.setScore(explain.getScore());
        
        usedFactorIds.add(factor.getId());
        
        // 处理子因子
        if (factor.getFactorIds() != null && !factor.getFactorIds().trim().isEmpty()) {
            String[] childIds = factor.getFactorIds().split(",");
            for (String childIdStr : childIds) {
                try {
                    Integer childId = Integer.parseInt(childIdStr.trim());
                    if (factorMap.containsKey(childId) && explainMap.containsKey(childId)) {
                        ScaleFactorDto childFactor = factorMap.get(childId);

                        // 递归构建子因子
                        FactorExplainHierarchyDto childNode = buildFactorHierarchy(
                                childFactor, explainMap, factorMap, usedFactorIds);
                        if (childNode != null) {
                            node.getChildren().add(childNode);
                        }
                    }
                } catch (NumberFormatException e) {
                    // 忽略无效的ID
                }
            }
        }
        return node;
    }

    /**
     * 构建Word报告基本信息
     * @param reportDto 报告数据
     * @param reportMap 报告Map
     */
    private void buildBasicInfo(ReportDto reportDto, Map<String, Object> reportMap) {
        if (reportDto == null || reportDto.getTestRecord() == null) {
            throw new RuntimeException("报告数据或测评记录为空");
        }

        TestRecordDto testRecord = reportDto.getTestRecord();
        UserDto user = testRecord.getUser();
        ScaleDto scale = testRecord.getScale();

        if (user == null) {
            throw new RuntimeException("用户信息为空");
        }
        if (scale == null) {
            throw new RuntimeException("量表信息为空");
        }

        // 基本信息 - 增加空值检查
        String userName = "";
        if (user.getRealName() != null && !user.getRealName().trim().isEmpty()) {
            userName = user.getRealName().trim();
        } else if (user.getLoginName() != null && !user.getLoginName().trim().isEmpty()) {
            userName = user.getLoginName().trim();
        } else {
            userName = "未知用户";
        }
        reportMap.put("userName", userName);

        String orgName = user.getStructName() != null ? user.getStructName().trim() : "";
        reportMap.put("orgName", orgName);

        String scaleName = scale.getScaleName() != null ? scale.getScaleName().trim() : "未知量表";
        reportMap.put("scaleName", scaleName);

        // 格式化测评日期
        if (testRecord.getStartTime() != null) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy年MM月dd日");
            reportMap.put("testDate", dateFormat.format(testRecord.getStartTime()));
        } else {
            reportMap.put("testDate", "");
        }

        // 格式化耗时
        if (testRecord.getTimeInterval() != null) {
            int totalSeconds = testRecord.getTimeInterval();
            int minutes = totalSeconds / 60;
            int seconds = totalSeconds % 60;
            reportMap.put("costTime", String.format("%d分%d秒", minutes, seconds));
        } else {
            reportMap.put("costTime", "");
        }

        // 其他信息
        reportMap.put("testId", testRecord.getId() != null ? testRecord.getId().toString() : "");
    }

    /**
     * 构建因子章节数据（完全按照前端逻辑处理）
     * @param factorHierarchy 因子层次结构
     * @return Word报告章节列表
     */
    private List<WordReportSection> buildFactorSections(List<FactorExplainHierarchyDto> factorHierarchy) {
        List<WordReportSection> sections = new ArrayList<>();

        if (factorHierarchy == null || factorHierarchy.isEmpty()) {
            return sections;
        }

        // 完全按照前端processFactorHierarchy的逻辑
        List<FactorExplainHierarchyDto> topLevelFactors = new ArrayList<>();
        List<ParentGroup> parentGroups = new ArrayList<>();
        List<FactorExplainHierarchyDto> independentFactors = new ArrayList<>();
        Set<Integer> processedFactorIds = new HashSet<>();

        // 第一步：处理顶层因子和直接子因子（对应前端846-858行）
        for (FactorExplainHierarchyDto factor : factorHierarchy) {
            if (factor.getChildren() != null && !factor.getChildren().isEmpty()) {
                topLevelFactors.add(factor);
                parentGroups.add(new ParentGroup(factor.getFactorId(), factor.getFactorName(), factor.getChildren()));
            } else if (!processedFactorIds.contains(factor.getFactorId())) {
                independentFactors.add(factor);
                processedFactorIds.add(factor.getFactorId());
            }
        }

        // 第二步：递归收集因子（对应前端collectFactors函数）
        collectFactors(factorHierarchy, null, parentGroups, independentFactors, processedFactorIds);

        // 第三步：按前端显示顺序生成章节
        // 1. generateTopLevelContent - 显示顶层因子（三级因子结构中的最顶层）
        if (!topLevelFactors.isEmpty()) {
            WordReportSection topLevelSection = new WordReportSection();
            topLevelSection.setSectionTitle("结果分析");
            topLevelSection.setFactors(convertToWordFactors(topLevelFactors));
            sections.add(topLevelSection);
        }

        // 2. generateParentGroupContent - 为每个parentGroup生成章节
        for (ParentGroup parentGroup : parentGroups) {
            WordReportSection section = new WordReportSection();
            section.setSectionTitle(parentGroup.parentName);
            section.setFactors(convertToWordFactors(parentGroup.children));
            sections.add(section);
        }

        // 3. generateIndependentFactorsContent - 独立因子
        if (!independentFactors.isEmpty()) {
            WordReportSection independentSection = new WordReportSection();
            independentSection.setSectionTitle("结果解释及建议");
            independentSection.setFactors(convertToWordFactors(independentFactors));
            sections.add(independentSection);
        }

        return sections;
    }

    /**
     * 递归收集因子（对应前端collectFactors函数）
     */
    private void collectFactors(List<FactorExplainHierarchyDto> factors, ParentInfo parentInfo,
                               List<ParentGroup> parentGroups, List<FactorExplainHierarchyDto> independentFactors,
                               Set<Integer> processedFactorIds) {
        for (FactorExplainHierarchyDto factorData : factors) {
            if (processedFactorIds.contains(factorData.getFactorId())) {
                continue;
            }
            processedFactorIds.add(factorData.getFactorId());

            if (factorData.getChildren() != null && !factorData.getChildren().isEmpty()) {
                collectFactors(factorData.getChildren(),
                    new ParentInfo(factorData.getFactorId(), factorData.getFactorName()),
                    parentGroups, independentFactors, processedFactorIds);
            } else {
                if (parentInfo != null && factorData.getFactorType() != null && factorData.getFactorType() == 1) {
                    // 找到对应的parentGroup并添加子因子
                    ParentGroup existingGroup = parentGroups.stream()
                        .filter(group -> group.parentId.equals(parentInfo.parentId))
                        .findFirst()
                        .orElse(null);

                    if (existingGroup != null) {
                        boolean exists = existingGroup.children.stream()
                            .anyMatch(child -> child.getFactorId().equals(factorData.getFactorId()));
                        if (!exists) {
                            existingGroup.children.add(factorData);
                        }
                    } else {
                        List<FactorExplainHierarchyDto> children = new ArrayList<>();
                        children.add(factorData);
                        parentGroups.add(new ParentGroup(parentInfo.parentId, parentInfo.parentName, children));
                    }
                } else {
                    boolean exists = independentFactors.stream()
                        .anyMatch(factor -> factor.getFactorId().equals(factorData.getFactorId()));
                    if (!exists) {
                        independentFactors.add(factorData);
                    }
                }
            }
        }
    }

    // 辅助类
    private static class ParentGroup {
        Integer parentId;
        String parentName;
        List<FactorExplainHierarchyDto> children;

        ParentGroup(Integer parentId, String parentName, List<FactorExplainHierarchyDto> children) {
            this.parentId = parentId;
            this.parentName = parentName;
            this.children = new ArrayList<>(children);
        }
    }

    private static class ParentInfo {
        Integer parentId;
        String parentName;

        ParentInfo(Integer parentId, String parentName) {
            this.parentId = parentId;
            this.parentName = parentName;
        }
    }



    /**
     * 将FactorExplainHierarchyDto转换为WordReportFactor
     * @param factor 因子数据
     * @return Word报告因子
     */
    private WordReportFactor convertToWordFactor(FactorExplainHierarchyDto factor) {
        if (factor == null) {
            throw new RuntimeException("因子数据为空");
        }

        WordReportFactor wordFactor = new WordReportFactor();

        // 因子名称 - 必须有值
        String factorName = factor.getFactorName();
        if (factorName == null || factorName.trim().isEmpty()) {
            factorName = "未知因子";
        }
        wordFactor.setFactorName(factorName.trim());

        // 解释内容
        String interpretation = factor.getInterpretation();
        if (interpretation == null || interpretation.trim().isEmpty()) {
            interpretation = "暂无解释内容";
        }
        wordFactor.setInterpretation(interpretation.trim());

        return wordFactor;
    }

    /**
     * 将因子列表转换为Word报告因子列表
     * @param factors 因子列表
     * @return Word报告因子列表
     */
    private List<WordReportFactor> convertToWordFactors(List<FactorExplainHierarchyDto> factors) {
        List<WordReportFactor> wordFactors = new ArrayList<>();
        for (FactorExplainHierarchyDto factor : factors) {
            wordFactors.add(convertToWordFactor(factor));
        }
        return wordFactors;
    }

    /**
     *  获取测评报告
     * @param recordId 测评记录id
     * @return 测评报告实体对象
     */
    @Override
    public ReportDto getReport(Integer recordId) {
        var reportDto = new ReportDto();
        reportDto.setTestRecord(getById(recordId));
        reportDto.setListExplains(getFactorExplainsWithHierarchy(recordId));
        reportDto.setListCharts(getReportCharts(recordId));
        return reportDto;
    }
}
