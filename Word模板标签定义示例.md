# Word模板标签定义示例

## 模板文件说明
模板文件路径：`src/main/resources/static/template/report_new.docx`

## 基本信息标签

### 用户信息
- `{{userName}}` - 用户姓名
- `{{orgName}}` - 所属组织
- `{{testId}}` - 测评记录ID

### 测评信息
- `{{scaleName}}` - 量表名称
- `{{testDate}}` - 测评日期（格式：2024年01月15日）
- `{{costTime}}` - 测评耗时（格式：15分30秒）

## 因子结果表格标签

### 章节循环结构
```
{{#factorSections}}
章节标题：{{sectionTitle}}

因子结果表格：
┌─────────────┬─────────────┬─────────────┬─────────────────────────────┐
│   因子名称   │   原始分数   │   标准分数   │           结果解释           │
├─────────────┼─────────────┼─────────────┼─────────────────────────────┤
{{#factors}}
│{{factorName}}│{{originalScore}}│{{score}}│{{interpretation}}│
{{/factors}}
└─────────────┴─────────────┴─────────────┴─────────────────────────────┘
{{/factorSections}}
```

## Word模板具体实现示例

### 1. 报告标题部分
```
《{{scaleName}}》测评报告

基本信息：
姓名：{{userName}}
组织：{{orgName}}
测评日期：{{testDate}}
测评耗时：{{costTime}}
```

### 2. 因子结果分析部分
```
{{#factorSections}}
## {{sectionTitle}}

| 因子名称 | 原始分数 | 标准分数 | 结果解释 |
|---------|---------|---------|---------|
{{#factors}}
| {{factorName}} | {{originalScore}} | {{score}} | {{interpretation}} |
{{/factors}}

{{/factorSections}}
```

## 数据结构说明

### factorSections 数组结构
```json
[
  {
    "sectionTitle": "结果分析",
    "factors": [
      {
        "factorName": "外向性",
        "originalScore": "65.00",
        "score": "55.50",
        "interpretation": "您在外向性方面表现为中等偏上水平..."
      }
    ]
  }
]
```

## 适用场景

### 1. 只有一级因子
```
factorSections = [
  {
    sectionTitle: "结果分析",
    factors: [
      {factorName: "外向性", originalScore: "65", score: "55", interpretation: "..."},
      {factorName: "神经质", originalScore: "45", score: "50", interpretation: "..."}
    ]
  }
]
```

### 2. 有父子因子结构
```
factorSections = [
  {
    sectionTitle: "人格特质",
    factors: [
      {factorName: "外向性", originalScore: "65", score: "55", interpretation: "..."},
      {factorName: "内向性", originalScore: "35", score: "45", interpretation: "..."}
    ]
  },
  {
    sectionTitle: "情绪稳定性",
    factors: [
      {factorName: "焦虑", originalScore: "40", score: "48", interpretation: "..."},
      {factorName: "抑郁", originalScore: "30", score: "42", interpretation: "..."}
    ]
  }
]
```

### 3. 三级因子结构
```
factorSections = [
  {
    sectionTitle: "认知能力 - 言语理解",
    factors: [
      {factorName: "词汇理解", originalScore: "85", score: "65", interpretation: "..."},
      {factorName: "语法分析", originalScore: "78", score: "60", interpretation: "..."}
    ]
  },
  {
    sectionTitle: "认知能力 - 数理逻辑",
    factors: [
      {factorName: "数字推理", originalScore: "92", score: "70", interpretation: "..."},
      {factorName: "逻辑分析", originalScore: "88", score: "68", interpretation: "..."}
    ]
  }
]
```

## 模板制作注意事项

1. **表格循环**：使用poi-tl的表格循环功能，确保`{{#factorSections}}`和`{{#factors}}`标签正确嵌套
2. **样式保持**：在Word模板中设置好表格样式，poi-tl会保持原有格式
3. **空数据处理**：如果某个字段为空，模板中会显示空白
4. **中文支持**：确保Word模板文件保存为UTF-8编码，支持中文显示
5. **表格宽度**：根据内容长度调整表格列宽，特别是"结果解释"列通常需要较宽

## 扩展功能

如需添加图表功能，可以在factorSections中增加图表相关字段：
```json
{
  "sectionTitle": "结果分析",
  "factors": [...],
  "chartImage": "base64编码的图片数据或图片路径"
}
```
