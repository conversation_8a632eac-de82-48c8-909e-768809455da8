# Word模板制作指南

## 模板文件位置
文件路径：`src/main/resources/static/template/report_new.docx`

## 模板内容结构

### 1. 文档标题
```
《{{scaleName}}》测评报告
```

### 2. 基本信息表格
```
┌─────────────┬─────────────────────────────┐
│    姓名     │         {{userName}}        │
├─────────────┼─────────────────────────────┤
│    组织     │         {{orgName}}         │
├─────────────┼─────────────────────────────┤
│  测评日期   │         {{testDate}}        │
├─────────────┼─────────────────────────────┤
│  测评耗时   │         {{costTime}}        │
└─────────────┴─────────────────────────────┘
```

### 3. 因子结果分析（核心部分）
```
{{#factorSections}}
{{sectionTitle}}

┌─────────────┬─────────────┬─────────────┬─────────────────────────────┐
│   因子名称   │   原始分数   │   标准分数   │           结果解释           │
├─────────────┼─────────────┼─────────────┼─────────────────────────────┤
{{#factors}}
│{{factorName}}│{{originalScore}}│{{score}}│{{interpretation}}│
{{/factors}}
└─────────────┴─────────────┴─────────────┴─────────────────────────────┘

{{/factorSections}}
```

## 在Word中的实际操作步骤

### 1. 创建基本文档结构
1. 打开Microsoft Word
2. 创建新文档
3. 设置页面格式（A4纸张，合适的页边距）

### 2. 插入标题
```
《{{scaleName}}》测评报告
```
- 设置为标题1样式
- 居中对齐
- 字体大小：18-20pt

### 3. 创建基本信息表格
1. 插入表格：2列4行
2. 填入标签内容
3. 设置表格样式（边框、对齐方式等）

### 4. 创建因子结果表格
1. 输入章节标题标签：`{{#factorSections}}`
2. 输入章节标题：`{{sectionTitle}}`
3. 插入表格：4列2行（表头+数据行）
4. 表头：因子名称 | 原始分数 | 标准分数 | 结果解释
5. 数据行填入对应标签
6. 在数据行前后添加循环标签：`{{#factors}}` 和 `{{/factors}}`
7. 在整个章节后添加结束标签：`{{/factorSections}}`

## 标签使用注意事项

### 1. 循环标签
- 开始标签：`{{#factorSections}}`
- 结束标签：`{{/factorSections}}`
- 嵌套循环：`{{#factors}}` ... `{{/factors}}`

### 2. 变量标签
- 简单变量：`{{variableName}}`
- 确保标签名称与Java代码中的字段名完全一致

### 3. 表格处理
- poi-tl会自动复制表格行来处理循环数据
- 只需要在模板中创建一行数据模板
- 循环标签要正确包围需要重复的行

### 4. 样式保持
- 在模板中设置的字体、颜色、对齐方式会被保留
- 建议使用Word的样式功能统一格式

## 测试模板

### 测试数据示例
```json
{
  "userName": "张三",
  "orgName": "技术部",
  "scaleName": "大五人格测验",
  "testDate": "2024年01月15日",
  "costTime": "15分30秒",
  "scaleIntro": "大五人格测验是心理学中最权威的人格测评工具...",
  "factorSections": [
    {
      "sectionTitle": "人格特质分析",
      "factors": [
        {
          "factorName": "外向性",
          "originalScore": "65.00",
          "score": "55.50",
          "interpretation": "您在外向性方面表现为中等偏上水平，喜欢与人交往..."
        },
        {
          "factorName": "神经质",
          "originalScore": "45.00",
          "score": "50.00",
          "interpretation": "您的情绪稳定性处于正常范围内..."
        }
      ]
    }
  ]
}
```

## 常见问题解决

### 1. 标签不生效
- 检查标签拼写是否正确
- 确认循环标签是否正确配对
- 验证Java代码中的字段名是否匹配

### 2. 表格格式异常
- 确保循环标签正确包围表格行
- 检查表格结构是否完整
- 验证表格样式设置

### 3. 中文显示问题
- 确保Word文档保存为UTF-8编码
- 检查字体是否支持中文
- 验证Java代码中的中文字符串

### 4. 空数据处理
- 空字段会显示为空白
- 可以在Java代码中设置默认值
- 或在模板中添加条件判断标签

## 扩展功能

### 添加条件显示
```
{{#hasScore}}
分数：{{score}}
{{/hasScore}}
```

### 添加图片
```
{{@chartImage}}
```

### 添加HTML格式文本
```
{{interpretation}}
```
（需要在POIWordHelper中配置HtmlRenderPolicy）
