# 更新后的Word模板标签说明

## 修改内容
已去掉原始分数和标准分数字段，只保留因子名称和结果解释，与前端显示保持一致。

## Word模板中的正确标签格式

### 1. 基本信息标签
```
《{{scaleName}}》测评报告

基本信息：
姓名：{{userName}}
组织：{{orgName}}
测评日期：{{testDate}}
测评耗时：{{costTime}}
```

### 2. 因子结果表格标签
```
{{#factorSections}}
{{sectionTitle}}

[创建2列表格]
┌─────────────┬─────────────────────────────┐
│   因子名称   │           结果解释           │
├─────────────┼─────────────────────────────┤
{{#factors}}
│{{factorName}}│{{interpretation}}│
{{/factors}}
└─────────────┴─────────────────────────────┘

{{/factorSections}}
```

## 在Word中的具体操作

### 1. 修正您当前模板中的问题

根据您提供的截图，需要修正以下问题：

#### 问题1：标签格式错误
- ❌ `{{ s c a l e N a m e }}` (有空格)
- ✅ `{{scaleName}}`

- ❌ `{{userName}}` (有下划线)
- ✅ `{{userName}}`

#### 问题2：表格结构需要调整
- 删除"原始分数"和"标准分数"列
- 只保留"因子名称"和"结果解释"两列

#### 问题3：标签位置
确保表格中的标签在正确的位置：
```
{{#factors}}
[在表格行中] {{factorName}} | {{interpretation}}
{{/factors}}
```

### 2. 正确的Word模板结构

```
《{{scaleName}}》测评报告

基本信息
姓名: {{userName}}
所属组织: {{orgName}}
测评日期: {{testDate}}
耗时: {{costTime}}

结果解释及建议

{{#factorSections}}
{{sectionTitle}}

因子名称 | 结果解释
{{#factors}}
{{factorName}} | {{interpretation}}
{{/factors}}

{{/factorSections}}
```

## 对应的Java字段

```java
// 基本信息
userName        -> 用户姓名
orgName         -> 组织名称
scaleName       -> 量表名称
testDate        -> 测评日期
costTime        -> 测评耗时

// 因子数据
factorSections  -> 因子章节列表
  sectionTitle  -> 章节标题
  factors       -> 因子列表
    factorName      -> 因子名称
    interpretation  -> 结果解释
```

## 修改建议

1. **清理标签格式**：移除所有标签中的空格和下划线
2. **简化表格**：改为2列表格（因子名称 | 结果解释）
3. **检查循环标签**：确保 `{{#factorSections}}` 和 `{{#factors}}` 正确配对
4. **测试基本功能**：先用简单的基本信息标签测试

## 测试用的简化模板

建议先创建一个简化版本进行测试：

```
《{{scaleName}}》测评报告

姓名：{{userName}}
组织：{{orgName}}
日期：{{testDate}}

{{#factorSections}}
{{sectionTitle}}

{{#factors}}
{{factorName}}：{{interpretation}}

{{/factors}}
{{/factorSections}}
```

这个简化版本不使用表格，更容易排查问题。如果这个版本能正常工作，再逐步添加表格格式。
